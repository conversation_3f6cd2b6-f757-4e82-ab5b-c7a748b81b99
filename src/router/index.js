import HelloWorld from '@/view/helloWorld'
import Base from '@/view/base'
import DecErpI from '@/view/dec/imported_cigarettes'
import CostManage from '@/view/costManage'
import Params from '@/view/params'
import Payment from '@/view/payment'
import importedCigarettes from '../view/importedCigarettes'
import audit from '../view/audit'
import BaseInfoCustomerParams from '@/view/baseInfoCustomerParams'
import Quo from "@/view/quo";
import incoming from "@/view/dec/incoming";
import auxiliaryMaterials from '@/view/auxiliaryMaterials'
import warehouse from "@/view/warehouse";


export default [
  ...HelloWorld,
  ...Base,
  ...BaseInfoCustomerParams,
  ...CostManage,
  ...Params,
  ...Payment,
  ...DecErpI,
  ...importedCigarettes,
  ...Quo,
  ...incoming,
  ...auxiliaryMaterials,
  ...warehouse,
  ...audit,
]

<template>
  <section>
    <a-card size="small" title="外商合同信息" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData" class="grid-container">
          <!-- 业务类型 -->
          <a-form-item name="businessType" :label="'业务类型'" class="grid-item" :colon="false">
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.businessType" id="businessType">
              <a-select-option v-for="item in productClassify.businessType" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 合同编号 -->
          <a-form-item name="contractNo" :label="'合同号'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.contractNo"/>
          </a-form-item>
          <!-- 客户 -->
          <a-form-item name="customerName" :label="'买方'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.customerName" id="customerName">
              <a-select-option v-for="item in buyerOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 供应商 -->
          <a-form-item name="supplierName" :label="'卖方'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.supplierName" id="supplierName">
              <a-select-option v-for="item in buyerOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 签约日期 -->
          <a-form-item name="signDate" :label="'签约日期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="showDisable"
              v-model:value="formData.signDate"
              id="signDate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-form-item>
          <!-- 合同生效期 -->
          <a-form-item name="contractStartDate" :label="'合同生效期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="showDisable"
              v-model:value="formData.contractStartDate"
              id="contractStartDate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-form-item>
          <!-- 合同有效期 -->
          <a-form-item name="contractEndDate" :label="'合同有效期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="showDisable"
              v-model:value="formData.contractEndDate"
              id="contractEndDate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-form-item>
          <!-- 签约地点 -->
          <a-form-item name="signPlace" :label="'签约地点'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.signPlace" id="signPlace">
              <a-select-option v-for="item in cityOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 签约地点(英文) -->
          <a-form-item name="signPlaceEn" :label="'签约地点(英文)'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.signPlaceEn"/>
          </a-form-item>
          <!-- 装运港 -->
          <a-form-item name="portOfShipment" :label="'装运港'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.portOfShipment" id="portOfShipment">
              <a-select-option v-for="item in customsPortOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 目的港 -->
          <a-form-item name="portOfDestination" :label="'目的港'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.portOfDestination" id="portOfDestination">
              <a-select-option v-for="item in customsPortOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 报关口岸 -->
          <a-form-item name="customsPort" :label="'报关口岸'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.customsPort" id="customsPort">
              <a-select-option v-for="item in customsPortOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 付款方式 -->
          <a-form-item name="paymentMethod" :label="'付款方式'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.paymentMethod" id="paymentMethod">
              <a-select-option v-for="item in productClassify.paymentMethodMap" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 币种 -->
          <a-form-item name="currency" :label="'币种'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.currency" id="currency">
              <a-select-option v-for="item in currencyOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="remark" :label="'备注'" class="grid-item merge-3" :colon="false">
            <a-textarea :disabled="showDisable" size="small" v-model:value="formData.remark"  :autosize="{ minRows: 3, maxRows: 10 }"/>
          </a-form-item>
          <!-- 制单人 -->
          <a-form-item name="insertUserName" :label="'制单人'" class="grid-item" :colon="false">
            <a-input disabled size="small" v-model:value="formData.insertUserName"/>
          </a-form-item>
          <!-- 制单时间 -->
          <a-form-item name="createTime" :label="'制单时间'" class="grid-item" :colon="false">
            <a-date-picker
              disabled
              v-model:value="formData.createTime"
              id="createTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-form-item>
          <!-- 单据状态 -->
          <a-form-item name="docStatus" :label="'单据状态'" class="grid-item" :colon="false">
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.docStatus" id="docStatus">
              <a-select-option v-for="item in productClassify.data_status" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 确认时间 -->
          <a-form-item name="confirmDate" :label="'确认时间'" class="grid-item" :colon="false">
            <a-date-picker
              disabled
              v-model:value="formData.confirmDate"
              id="confirmDate"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
              showTime
            />
          </a-form-item>
          <!-- 审批状态 -->
          <a-form-item name="auditStatus" :label="'审核状态'" class="grid-item" :colon="false">
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.auditStatus" id="auditStatus">
              <a-select-option v-for="item in productClassify.audit_status" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
<!--          &lt;!&ndash; 版本号 &ndash;&gt;-->
<!--          <a-form-item name="versionNo" :label="'版本号'" class="grid-item" :colon="false">-->
<!--            <a-input disabled size="small" v-model:value="formData.versionNo"/>-->
<!--          </a-form-item>-->

          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !== editStatus.SHOW">保存
            </a-button>
            <!--            <a-button size="small" type="primary" @click="handlerSaveClose" class="cs-margin-right"-->
            <!--                      v-show="props.editConfig.editStatus !== editStatus.SHOW">保存关闭-->
            <!--            </a-button>-->
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>
    <a-card size="small"  class="cs-card-form">
      <contract-detail-table
        ref="detailTableRef"
        :disabled="showDisable"
        :head-id="formData.id"
        :edit-config="props.editConfig"
        @change="handleDetailChange"
      />
    </a-card>
  </section>
</template>

<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message} from "ant-design-vue";
import {onMounted, reactive, ref, computed, watch} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
import {deleteContract, updateContract} from "@/api/auxiliaryMaterials/forContract/contractApi";
import ContractDetailTable from './list/ContractDetailTable.vue';
import ycCsApi from "@/api/ycCsApi";

const { getPCode } = usePCode()

const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);

//首次新增未保存 删除数据
let firstAddSave = ref(false);

const onBack = (val) => {
  if (props.editConfig.editStatus === editStatus.ADD && !firstAddSave) {
    deleteContract(formData.id).then(res => {
      emit('onEditBack', val);
    })
  } else {
    emit('onEditBack', val);
  }
};

// 是否禁用
const showDisable = ref(false)

// 表单数据
const formData = reactive({
  'id':'',
  'businessType':'',
  'contractNo':'',
  'customerName':'',
  'supplierName':'',
  'signDate':'',
  'contractStartDate':'',
  'contractEndDate':'',
  'signPlace':'',
  'signPlaceEn':'',
  'portOfShipment':'',
  'portOfDestination':'',
  'customsPort':'',
  'paymentMethod':'',
  'currency':'',
  'remark':'',
  'docStatus':'',
  'confirmDate':'',
  'auditStatus':'',
  'dataState':'',
  'versionNo':'',
  'tradeCode':'',
  'sysOrgCode':'',
  'createBy':'',
  'createTime':'',
  'updateBy':'',
  'updateTime':'',
  'insertUserName':'',
  'updateUserName':''
})

// 表单校验规则
const rules = {
  businessType: [
    {required: true, message: '请选择业务类型', trigger: 'change'},
    {max: 60, message: '业务类型不能超过60个字符', trigger: 'blur'}
  ],
  planNo: [
    {required: true, message: '请输入计划编号', trigger: 'blur'},
    {max: 60, message: '计划编号不能超过60个字符', trigger: 'blur'}
  ],
  customerName: [
    {required: true, message: '请选择买方', trigger: 'change'},
    {max: 200, message: '买方不能超过200个字符', trigger: 'blur'}
  ],
  supplierName: [
    {required: true, message: '请选择卖方', trigger: 'change'},
    {max: 200, message: '卖方不能超过200个字符', trigger: 'blur'}
  ],
  contractNo: [
    {required: true, message: '请输入合同编号', trigger: 'blur'},
    {max: 60, message: '合同编号不能超过60个字符', trigger: 'blur'}
  ],

  insertUserName: [
    {required: true, message: '请输入制单人', trigger: 'blur'},
    {max: 10, message: '制单人不能超过10个字符', trigger: 'blur'}
  ],
  createTime: [
    {required: true, message: '请选择制单时间', trigger: 'blur'}
  ],
  versionNo: [
    {required: true, message: '请输入版本号', trigger: 'blur'},
    {max: 10, message: '版本号不能超过10个字符', trigger: 'blur'}
  ]
}


// 表单引用
const formRef = ref()

// 表格引用
const detailTableRef = ref();

// 处理明细数据变化
const handleDetailChange = (details) => {
  formData.details = details;
};

// 监听签约地点变化，自动填充英文签约地点
watch(() => formData.signPlace, (newSignPlace) => {
  if (newSignPlace && cityEnOptions.length > 0) {
    // 根据选中的签约地点key从cityEnOptions中找到对应的英文名称
    const selectedCity = cityEnOptions.find(city => city.value === newSignPlace);
    if (selectedCity) {
      formData.signPlaceEn = selectedCity.label;
    }
  } else if (!newSignPlace) {
    // 如果清空了签约地点，也清空英文签约地点
    formData.signPlaceEn = '';
  }
});


// 修改保存处理函数
const handlerSave = async () => {
  try {
    await formRef.value.validate()
    // 根据编辑状态判断是新增还是修改
    if (props.editConfig.editStatus === editStatus.ADD) {
      // 新增逻辑
      updateContract(formData.id, formData).then((res)=>{
        if (res.code === 200){
          message.success('保存成功')
          firstAddSave = true
          // 保存成功后刷新明细表格数据
          if (detailTableRef.value) {
            detailTableRef.value.reloadData();
          }
        } else {
          message.error(res.message);
        }
      })
    } else if (props.editConfig.editStatus === editStatus.EDIT) {
      updateContract(formData.id, formData).then((res)=>{
        if (res.code === 200){
          message.success('修改成功!')
          // 保存成功后刷新明细表格数据
          if (detailTableRef.value) {
            detailTableRef.value.reloadData();
          }
        } else {
          message.error(res.message);
        }
      })
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

//获取城市参数
const cityOptions = reactive([])
const cityEnOptions =  reactive([])

const getCityOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.params.city.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        cityOptions.push({
          value: item.paramCode,
          label: item.cityCnName
        });
        cityEnOptions.push({
          value: item.paramCode,
          label: item.cityEnName
        })
      });
    } else {
      message.error(res.message || '获取城市数据失败');
    }
  } catch (error) {
    message.error('获取城市数据失败');
  }
}

//基础资料-客商信息
const buyerOptions = reactive([])

const getBuyerOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.bizMerchant.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        buyerOptions.push({
          value: item.merchantCode,
          label: item.merchantNameCn
        });
      });
    } else {
      message.error(res.message || '获取客商数据失败');
    }
  } catch (error) {
    message.error('获取客商数据失败');
  }
}

//海关参数币制
const currencyOptions = reactive([]);

const getCurrency = async () => {
  const params = {
    paramsType: 'CURR',
  }
  try {
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.importedCigarettes.contract.customsList}/2`,
      params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        currencyOptions.push({
          value: item.key,
          label: item.value
        });
      });
    } else {
      message.error(res.message || '获取国家数据失败');
    }
  } catch (error) {
    message.error('获取国家数据失败');
  }
}

//海关参数港口
const customsPortOptions = reactive([]);

const getCustomsPortOptions = async () => {
  const params = {
    paramsType: 'PORT',
  }
  try {
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.importedCigarettes.contract.customsList}/2`,
      params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        customsPortOptions.push({
          value: item.key,
          label: item.value
        });
      });
    } else {
      message.error(res.message || '获取港口数据失败');
    }
  } catch (error) {
    message.error('获取港口数据失败');
  }
}

const pCode = ref('')

// 组件挂载时根据编辑状态设置表单数据和禁用状态
onMounted(() => {
  getPCode().then(res=>{
    console.log('res',res)
    pCode.value = res;
  })
  firstAddSave = false;
  // 获取港口数据
  getCustomsPortOptions();
  // 获取国家数据
  getCurrency();
  //获取客商信息
  getBuyerOptions();
  //获取城市
  getCityOptions();
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
})
</script>

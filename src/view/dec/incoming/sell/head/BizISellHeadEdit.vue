<template>
  <section>

    <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
            :model="formData" >
      <a-card size="small" title="销售信息表头" class="cs-card-form">
        <div class="cs-form grid-container">
<!--          进货单号-->
          <a-form-item name="purchaseOrderNumber" :label="'进货单号'" class="grid-item" :colon="false">
            <a-input :disabled="true" disabled size="small" v-model:value="formData.purchaseOrderNumber"/>
          </a-form-item>
<!--          合同号-->
          <a-form-item name="contractNo" :label="'合同号'" class="grid-item" :colon="false">
            <a-input :disabled="true" disabled size="small" v-model:value="formData.contractNo"/>
          </a-form-item>
<!--          客户-->
          <a-form-item name="customer" :label="'客户'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable||showSellDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.customer" id="customer">
              <a-select-option class="cs-select-dropdown" v-for="item in buyerOptions"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
<!--          销货单位-->
          <a-form-item name="sellingUnit" :label="'销货单位'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.sellingUnit"/>
          </a-form-item>
<!--          税率%-->
          <a-form-item name="taxRate" :label="'税率'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small" v-model:value="formData.taxRate"
                            :formatter="value => `${value}%`"
                            :parser="value => value.replace('%', '')"
                            notConvertNumber decimal int-length="19" precision="4"/>
          </a-form-item>
<!--          作销日期-->
          <a-form-item name="dateOfSale"   :label="'作销日期'" class="grid-item"  :colon="false">
            <a-date-picker
              :disabled="showDisable||showSellDisable"
              v-model:value="formData.dateOfSale"
              id="dateOfSale"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>
<!--          数据状态-->
          <a-form-item name="salesDocumentStatus" :label="'数据状态'" class="grid-item" :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.salesDocumentStatus" id="salesDocumentStatus">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.state"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
<!--          确认时间-->
          <a-form-item name="salesDataConfirmationTime" :label="'确认时间'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="true"
              v-model:value="formData.salesDataConfirmationTime"
              id="salesDataConfirmationTime"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>
<!--          发送财务系统-->
          <a-form-item name="sendFinancial" :label="'发送财务系统'" class="grid-item" :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.sendFinancial" id="sendFinancial">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.isNot"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
<!--          是否冲红-->
          <a-form-item name="isFlushRed" :label="'是否冲红'" class="grid-item" :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.isFlushRed" id="isFlushRed">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.isNot"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
<!--          备注-->
          <a-form-item name="remark" :label="'备注'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable||showSellDisable" size="small" v-model:value="formData.remark"/>
          </a-form-item>
<!--          开票人-->
          <a-form-item name="drawer" :label="'开票人'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.drawer"/>
          </a-form-item>
<!--          业务日期-->
          <a-form-item name="businessDate" :label="'业务日期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="showDisable||showSellDisable"
              v-model:value="formData.businessDate"
              id="businessDate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>


          <div class="cs-submit-btn merge-3">

            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right" :disabled="showSellDisable"
                      v-show="!showDisable">保存
            </a-button>
<!--            <a-button size="small" type="primary" @click="handlerSaveClose" class="cs-margin-right"-->
<!--                      v-show="!showDisable">保存关闭-->
<!--            </a-button>-->
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>

            <!-- 确认 -->
            <a-button size="small" type="ghost"   @click="handlerConfirm" class="cs-margin-right"
                      v-show="!showDisable" :loading="confirmOrderLoading">
              <template #icon>
                <GlobalIcon class="btn-icon" type="check"  style="font-size: 12px;"/>
              </template>
              <template #default>
                确认
              </template>
            </a-button>
<!--            <a-button  size="small" :loading="chargebackLoading" @click="handlerChargeback" v-show="!showDisable">-->
<!--              <template #icon>-->
<!--                <GlobalIcon type="swap-left" style="color:orange"/>-->
<!--              </template>-->
<!--              退单-->
<!--            </a-button>-->
            </div>

        </div>
      </a-card>
    </a-form>


    <a-card v-if="isShow" size="small" title="销售信息表体" class="cs-card-form" >
      <biz-i-sell-list-list ref="childRef" @save="handlerSave" :head-id="formData.sid" :stateMessage = "formData.salesDocumentStatus" :sellListMessage = "sellListMessage" @child-to-parent="handlerConfirmSellList" :editConfig="props.editConfig"/>
    </a-card>

    <a-modal :visible="isShowConfirm" :title="'提醒'" style="width: 15%" :footer="false" :closable="false">
      确认执行此操作吗？
      <a-form-item name="dateOfSale"  :label="'作销日期'" class="grid-item"  :colon="false">
        <a-date-picker
          v-model:value="formData.dateOfSale"
          id="dateOfSale"
          valueFormat="YYYY-MM-DD"
          format="YYYY-MM-DD"
          :locale="locale"
          size="small"
          style="width: 100%"
          placeholder=""
          @change = "handleDateChange"
        ></a-date-picker>
      </a-form-item>
      <div style="display: flex; justify-content: flex-end;">
        <a-button key="back" @click="handlerCloseConfirm">取消</a-button>
        <a-button key="submit" type="primary" :loading="loadingConfirm" @click="handlerOpenConfirm" style="margin-left: 8px;">确认</a-button>
      </div>
    </a-modal>

  </section>
</template>

<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {createVNode, h, onMounted, reactive, ref} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
import {
  getISellHeadMessage,
  getISellHeadUpdate,
  confirmIncoming, getISellchargebackIncoming,
} from "@/api/cs_api_constant";
import BizISellListList from "@/view/dec/incoming/sell/list/BizISellListList.vue";
import ycCsApi from "@/api/ycCsApi";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import {useOrderColumnsCommon} from "@/view/dec/imported_cigarettes/head/useOrderColumnsCommon";

const {getPCode} = usePCode()

const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  },

});

const { supplierList,getSupplierList} = useOrderColumnsCommon()
// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);
// 获取子组件的引用
const childRef = ref(null);

const onBack = (val) => {
  emit('onEditBack', val);
};

// 定义组件名称
defineOptions({
  name: 'BizISellHeadEdit'
})


// 是否禁用
const chargebackLoading = ref(false)
const showDisable = ref(false)
const showSellDisable = ref(false)
const isShow = ref(false)
const sellListMessage = ref([])
const loading = ref(false);
const loadingConfirm = ref(false);
const buyerOptions = reactive([])
// 表单数据
const formData = ref({
  // 主建sid
  sid: '',
  purchaseOrderNumber: '', // 进货单号
  contractNo: '', // 合同号
  customer: '', // 客户
  sellingUnit: '',        // 销货单位
  taxRate: '',            // 税率%
  dateOfSale: '',         // 作销日期
  salesDocumentStatus: '',// 销售单据状态
  salesDataConfirmationTime: '', // 销售数据确认时间
  sendFinancial: '', // 发送财务系统
  isFlushRed: '', // 是否冲红
  remark: '',             // 备注
  drawer: '',             // 开票人
  businessDate: ''        // 业务日期
})
const maintain = ref({
  salesContractNumber:'',
  salesInvoiceNumber:'',
  sids: []
})
// 校验规则
const rules = {
  customer: [
    {required: true}
  ],
  remark: [
    {max: 200, message: '备注长度不能超过200位字节', trigger: 'blur'}
  ],
  businessDate: [
    {required: true,type: 'date', message: '请选择有效日期格式', trigger: 'change'}
  ]
}

const pCode = ref('')
// 初始化操作
onMounted(() => {
  getPCode().then(res => {
    pCode.value = res;
  })
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    showDisable.value = true
  }
  getList()
  getBuyerOptions()
});

const getBuyerOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.bizMerchant.list}`, params
    );
    if (res.code === 200) {
      console.log(res)
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        buyerOptions.push({
          value: item.merchantCode,
          label: item.merchantNameCn
        });
      });
    } else {
      message.error(res.message || '获取客商数据失败');
    }
  } catch (error) {
    message.error('获取客商数据失败');
  }
}
const getList = () => {
  var json = {'headId':props.editConfig.editData.id}
  //初始化数据
  getISellHeadMessage(json).then((res) => {
    if (res.code === 200) {
      if(res.data){
        formData.value = res.data
      }
      if(formData.value.salesDocumentStatus === '1' ||formData.value.salesDocumentStatus === '2'){
        showSellDisable.value = true
      }else{
        showSellDisable.value = false
      }
      isShow.value = true
    }
  })
};

/* 发票号汇总显示 */
const invoiceTotalRef = ref()
const isShowConfirm = ref(false)
const handleDateChange = () =>{
  handlerSave()
}
// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
const formRef = ref(null);
const handlerSaveClose = () => {
  if(showSellDisable.value === true){
    return message.error('仅编制状态可编辑！')
  }
  formRef.value
    .validate()
    .then(() => {
      console.log("-------------")
      console.log(formData.value.sid)
      if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
        getISellHeadUpdate(formData.value.sid, formData.value).then((res) => {
          if (res.code === 200) {
            message.success('修改成功!')
            onBack(true)
          }
        })
      }
    })
    .catch(error => {
      console.log('validate failed', error);
    })
}

// 保存
const handlerSave = () => {
  if(showSellDisable.value === true){
    return message.error('仅编制状态可编辑!')
  }
  formRef.value
    .validate()
    .then(() => {
      console.log("-------------")
      console.log(formData.value.sid)
      if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
        getISellHeadUpdate(formData.value.sid, formData.value).then((res) => {
          if (res.code === 200) {
            message.success('表头保存成功!')
            formData.value = res.data
            console.log('res',res.data)
            onBack({
              editData: null,
              showBody: true,
              editStatus: editStatus.EDIT,
              showBodyWarehouseReceiptHead:true,
              showBodyPurchaseHead:true,
              showBodyReceiptSell: true,
            })
          }
        })
      }
    })
    .catch(error => {
      console.log('validate failed', error);
    })
};


/* 销售表头确认 */
const confirmOrderLoading = ref(false)
const handlerCloseConfirm = (val)=>{
  isShowConfirm.value = false
}
const handlerOpenConfirm = () => {
  loadingConfirm.value = true;
  // 这里需要调用确认API
  const params = {
    sid : props.editConfig.editData.id
  }
  confirmIncoming(params).then(res => {
    if (res.code === 200) {
      message.success(res.message)
    }else{
      message.error(res.message)
    }
  }).finally(() => {
    isShowConfirm.value = false
    getList()
  })
  isShowConfirm.value = false
}
const handlerConfirm = () => {
  if(formData.value.salesDocumentStatus === '2'){
    return message.error('作废状态无法退单!')
  }
  if(formData.value.salesDocumentStatus === '1' ){
    return message.error('该数据已经确认，无需重复操作!')
  }
  loadingConfirm.value = false
  isShowConfirm.value = true
}
const handlerChargeback = () => {
  if(formData.value.salesDocumentStatus === '0'){
    return message.error('仅确认状态可以退单!')
  }
  if(formData.value.salesDocumentStatus === '2'){
    return message.error('作废状态无法退单!')
  }
  //弹出确认框
  Modal.confirm({
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '退单',
    cancelText: '取消',
    content: '确认退单所选项吗？',
    onOk() {
      chargebackLoading.value = true
      getISellchargebackIncoming(formData.value.sid).then(res => {
        if (res.code === 200) {
          message.success("退单成功！")
          getList()
        }
      }).finally(() => {
        chargebackLoading.value = false
      })
    },
    onCancel() {

    },
  });
}
const handlerConfirmSellList = (sellList) => {
  sellListMessage.value = sellList
}

</script>

<style lang="less" scoped>


</style>




<template>
  <section  class="dc-section">
    <div class="cs-action"  >

      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
<!--          <div class="cs-action-btn-item" v-has="['yc-cs:notifyList:edit']">-->
<!--            <a-button  v-show="props.editConfig.editStatus === editStatus.EDIT" size="small"  @click="handlerEdit">-->
<!--              <template #icon>-->
<!--                <GlobalIcon type="form" style="color:orange"/>-->
<!--              </template>-->
<!--              {{localeContent('m.common.button.update')}}-->
<!--            </a-button>-->
<!--          </div>-->
          <div class="cs-action-btn-item" v-has="['yc-cs:notifyList:delete']">
            <a-button  v-show="props.editConfig.editStatus === editStatus.EDIT" size="small" :loading="deleteLoading" @click="handlerDelete">
              <template #icon>
                <GlobalIcon type="delete" style="color:red"/>
              </template>
              {{localeContent('m.common.button.delete')}}
            </a-button>
          </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:notifyList:delete']">
          <a-button  v-show="props.editConfig.editStatus === editStatus.EDIT" size="small"  @click="handlerAddList">
            <template #icon>
              <GlobalIcon type="plus" style="color:green"/>
            </template>
            新增明细
          </a-button>
        </div>
          <div class="cs-action-btn-item" v-has="['yc-cs:notifyList:export']">
            <a-button  size="small" :loading="exportLoading" @click="handlerExport">
              <template #icon>
                <GlobalIcon type="folder-open" style="color:orange"/>
              </template>
              {{localeContent('m.common.button.export')}}
            </a-button>
          </div>



        <div class="cs-action-btn-settings">
          <!-- 自定义显示组件 -->
          <CsTableColSettings
            :resId="tableKey"
            :tableKey="tableKey+'-client_code'"
            :initSettingColumns="originalColumns"
            :showColumnSettings="true"
            @customColumnChange="customColumnChange"
          >
          </CsTableColSettings>
        </div>





      </div>

      <!-- 表格区域 -->
      <div  v-if="showColumns && showColumns.length > 0">
        <s-table
          :animate-rows="false"
          ref="tableRef"
          class="cs-action-item-modal-table remove-table-border-add-bg"
          size="small"
          :scroll="{ y: tableHeight,x:400 }"
          bordered
          column-drag
          :pagination="false"
          :columns="showColumns.length > 0 ?showColumns:totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="sid"
        >
          <!-- 操作 -->

          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'qty'">
              {{ formatNumber(record.qty) }}
            </template>
          </template>

          <template v-if="props.editConfig.editStatus !== editStatus.SHOW" #cellEditor="{ column, modelValue, save, closeEditor, editorRef, getPopupContainer, record }">
            <template v-if="column.dataIndex === 'qty'">


              <a-input-number
                :ref="editorRef"
                size="small"
                v-model:value="modelValue.value"
                style="width: 100%;height: 24px"
                :formatter="formatter"
                :parser="parser"
                @keydown.enter="() => {
    handleQtyChange(record, modelValue.value);
  }"
                @keydown.esc="closeEditor"
              />
            </template>
          </template>

<!--          <template #bodyCell="{ column, text, record }">-->
<!--            <template v-if="['qty'].includes(column.dataIndex)">-->
<!--                <span v-if="editableData[record.sid]">-->
<!--                              <a-input-number-->
<!--                                v-model:value="editableData[record.sid][column.dataIndex]"-->
<!--                                style="margin: -5px 0"-->
<!--                                :formatter="value => value ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') : '0'"-->
<!--                                :parser="value => value.replace(/\$\s?|(,*)/g, '')"-->
<!--                                @change="value => {-->
<!--                                          editableData[record.sid][column.dataIndex] = value;-->
<!--                                        }"-->
<!--                              />-->
<!--              </span>-->
<!--              <span v-else-if="['qty'].includes(column.dataIndex)">-->
<!--                    {{ formatNumber(text) }}-->
<!--              </span>-->
<!--              <span v-else>-->
<!--                    {{ text }}-->
<!--              </span>-->
<!--            </template>-->
<!--            <template v-else-if="column.dataIndex === 'operation'">-->
<!--              <div class="editable-row-operations">-->
<!--          <span v-if="editableData[record.sid]">-->
<!--            <a-typography-link @click="save(record.sid)">保存 </a-typography-link>-->
<!--             <a @click="cancel(record.sid)">取消</a>-->
<!--          </span>-->
<!--                <span v-else>-->
<!--            <a @click="edit(record.sid)">编辑</a>-->
<!--          </span>-->
<!--              </div>-->
<!--            </template>-->
<!--          </template>-->
        </s-table>
      </div>
      <!-- 分页 -->
      <div class=cs-pagination           v-if="showColumns && showColumns.length > 0">
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
          <template #buildOptionText="props">
            <span >{{ props.value }}条/页</span>
          </template>
        </a-pagination>

      </div>
    </div>

    <!-- 新增 编辑数据 -->


    <div>
      <!-- 使用原生 <dialog> 元素 -->
      <cs-modal :visible="open" :title="'新增明细'" style="width: 80%" :footer="false" :closable="false">
        <template #customContent>
          <NotifyDetailTab v-if="modelView" :editConfig="props.editConfig" @onEditBack="handleCancel" @onHeadback="returnData" />
        </template>
      </cs-modal>
    </div>
    <!-- 导入数据 -->
<!--    <ImportIndex :importShow="importShow" :importConfig="importConfig"   @onImportSuccess="importSuccess"></ImportIndex>-->


  </section>


</template>

<script setup>
/* 使用自定义 Hook 函数 */
import {useCommon} from '@/view/common/useCommon'
import {createVNode, nextTick, onMounted, provide, reactive, ref, watch} from "vue";
import {getColumns} from "@/view/payment/notify/list/NotifyListColumns";
import {message, Modal} from "ant-design-vue";
import BreadCrumb from "@/components/breadcrumb/BreadCrumb.vue";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import {
  deleteCostType,
  getSnoCostType,
  insertCostType,
  updateCostType,

} from "@/api/params/params_info";
const { totalColumns} = getColumns()
import {ImportIndex} from 'yao-import'
import {localeContent} from "@/view/utils/commonUtil";
import { useImport } from "@/view/common/useImport";
import ycCsApi from "@/api/ycCsApi";
import CsTableColSettings from "@/components/settings/CsTableColSettings.vue";
import {useRoute} from "vue-router";
import {editStatus, productClassify} from "@/view/common/constant";
import {deepClone, isNullOrEmpty} from "@/view/utils/common";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {deleteNotifyList, updateNotifyList} from "@/api/payment/payment_info";
import CsModal from "@/components/modal/cs-modal.vue";
import NotifyDetailTab from "@/view/payment/notify/DetailTab/NotifyDetailTab.vue";
const { importConfig } = useImport()
const { cmbShowRender } = useColumnsRender()
// 格式化数字为千分位分隔的工具函数
const formatNumber = (value) => {
  if (value === undefined || value === null || value === '') {
    return '';
  }
  // 将数字转换为字符串并添加千分位分隔符
  return new Intl.NumberFormat('zh-CN').format(value);
};

/* 引入通用方法 */
const {
  editConfig,
  // show,
  page,
  showSearch,
  headSearch,
  // handleEditByRow,
  // handleViewByRow,
  operationEdit,
  // onPageChange,
  handleShowSearch,
  handlerSearch,
  dataSourceList,
  tableLoading,
  getTableScroll,
  exportLoading,
  ajaxUrl,
  handlerRefresh,
  gridData

} = useCommon()



defineOptions({
  name: 'NotifyList',
});

const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});
const emit = defineEmits(['onHeadback'])

const tableRef = ref(null);
const editableData = reactive({});
const importShow = ref(false)
const open = ref(false)
const modelView = ref(false);
onMounted(fn => {

  ajaxUrl.exportUrl = ycCsApi.payment.notifyList.export
  ajaxUrl.selectAllPage = ycCsApi.payment.notifyList.list

  tableHeight.value = getTableScroll(100,'');

  getList()

  initCustomColumn()


})
const tableData = ref([]);
const editableKeys = ref([]);
const dataSource = ref([]);
const tableHeight = ref('')

const handlerAddList = ()=>{
  modelView.value = true;
  open.value = true;
}

const handleCancel = async () => {
  modelView.value = false;
  open.value = false;
  // 等待 DOM 更新完成
  await nextTick();
  await getList()
};

const returnData = (val) => {
  emit('onHeadback', val);
};

function onPageChange(pageNumber, pageSize) {
  page.current = pageNumber
  page.pageSize = pageSize
  // 在这里添加处理页码变化的逻辑
  getList()
}


function formatter(value) {
  if (value === '' || value === null || value === undefined) return '';
  const number = Number(value);
  if (isNaN(number)) return '';
  // 使用 Intl.NumberFormat 格式化，强制至少两位小数
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 10
  }).format(number);
}
function parser(value) {
  // 去除所有千分位逗号，保留原始小数位
  return value.replace(/,/g, '');
}

// 获取列表数据
const getList = async () => {
  try {
    const params = {
      headId: props.editConfig.editData.sid
    };
    window.majesty.httpUtil.postAction(`${ajaxUrl.selectAllPage}?page=${page.current}&limit=${page.pageSize}`,
        params
    ).then(res => {
      if (res.code === 200) {
        dataSourceList.value = res.data || [];
        editableKeys.value = dataSourceList.value.map(item => item.sid);
        page.total = res.total

        // 数据加载完成后，如果不是查看模式，触发编辑器
        if (props.editConfig.editStatus !== editStatus.SHOW) {
          setTimeout(() => {
            triggerEditor();
          }, 200);
        }
      } else {
        message.error(res.message || '获取数据失败');
      }
    })
  } catch (error) {
    message.error('获取数据失败'+error);
  }
};

// 监听编辑状态变化，当进入编辑状态时自动触发编辑器
watch(() => props.editConfig.editStatus, (newVal) => {
  if (newVal !== editStatus.SHOW && dataSourceList.value.length > 0) {
    nextTick(() => {
      triggerEditor();
    });
  }
});


// 触发编辑器打开
const triggerEditor = () => {
  if (props.editConfig.editStatus !== editStatus.SHOW) {
    // 确保使用最新的表格数据
    dataSource.value = [...dataSourceList.value];

    // 构建所有行的编辑配置
    const editConfigs = [];
    dataSource.value.forEach(row => {
      editConfigs.push({ columnKey: 'qty', rowKey: row.sid });
    });

    // 使用nextTick确保DOM已更新
    nextTick(() => {
      console.log('触发编辑器，行数:', dataSource.value.length, '编辑配置:', editConfigs);
      if (editConfigs.length > 0) {
        // 一次性打开所有单元格的编辑状态
        tableRef.value?.openEditor(editConfigs);
      }
    });
  }
};


// 处理合同数量变更
const handleQtyChange = async (record, newValue) => {
  if (newValue === record.qty) {
    return;
  }

  try {
    // 更新本地数据
    const rowIndex = dataSourceList.value.findIndex(item => item.sid === record.sid);
    if (rowIndex !== -1) {
      // 这里调用后端API
      const updatedRecord = { ...record, qty: newValue };
      const response = await updateNotifyList(record.sid, updatedRecord);
      if (response.code === 200) {
        // message.success('修改成功!');
        Object.assign(dataSourceList.value[rowIndex], response.data);
        returnData(response.data)
        await getList();
      } else {
        message.error(response.message || '修改失败');
      }
    }
  } catch (error) {
    message.error('数据更新失败，请重试');
  }
};

function filterExportParams(params) {
  let tempArr = []
  if (isNullOrEmpty(params)) {
    return []
  }
  params.value.forEach(item => {
    // 如果当前字段中dataIndex是operation 则不导出
    if (item.dataIndex !== 'operation') {
      tempArr.push({
        key: item.dataIndex,
        value: item.title
      })
    }
  })
  return tempArr
}


function doExport(fileName, exportHeader, exportColumns) {
  exportLoading.value = true
  window.window.majesty.httpUtil.downloadFile( "/biz/api/" + 'v1/notifyList/export',
      fileName,
      {
        exportColumns: {headid : props.editConfig.editData.sid},
        name: fileName,
        header: exportHeader ? filterExportParams(exportHeader) : []
      },
      'post',
      () => {
      }
  ).then((res) => {

  }).finally(() => {
    exportLoading.value = false
  })
}




/* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
const onSelectChange = (selectedRowKeys, rowSelectData) => {
  gridData.selectedData = rowSelectData;
  gridData.selectedRowKeys = selectedRowKeys;
};


/* 按钮loading */
const deleteLoading = ref(false)


const arrayToCmbShowRender = (textArray,commonMarkMap) => {
  let stringR = '';
  if(textArray !== null){
    // const array = textArray.split(",");
    textArray.forEach(item => {
      stringR = stringR +','+ cmbShowRender(item,commonMarkMap)
    })
    return stringR.substring(1);
  }

};




/* 编辑数据 */
const handlerEdit = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  let sid = gridData.selectedRowKeys[0]

  editableData[sid] = deepClone(dataSourceList.value.filter(item => sid === item.sid)[0]);
}


/* 删除数据 */
const handlerDelete = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒?',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '删除',
    cancelText: '取消',
    content: '所选中的明细中存在相同的进货单号，将一起删除，请确认',
    onOk() {
      deleteLoading.value = true
      const params = {
        headId: props.editConfig.editData.sid,
        sids: gridData.selectedRowKeys
      };
      deleteNotifyList(params).then(res => {
        if (res.code === 200) {
          message.success("删除成功！")
          returnData(res.data)
          getList()
        }
      }).finally(() => {
        deleteLoading.value = false
      })
    },
    onCancel() {

    },
  });

}

const edit = (sid) => {
  editableData[sid] = deepClone(dataSourceList.value.filter(item => sid === item.sid)[0]);
};
const save = (sid) => {

  Object.assign(dataSourceList.value.filter(item => sid === item.sid)[0], editableData[sid]);
  // 1. 找到数据源中与当前 sid 匹配的项
  const targetItem = dataSourceList.value.find(item => item.sid === sid);

  // 2. 将临时编辑的数据（editableData[sid]）合并到目标项中
  Object.assign(targetItem, editableData[sid]);


  // 3. 删除临时编辑的数据，清理内存
  delete editableData[sid];

    //执行保存请求
  updateNotifyList(sid,targetItem).then((res)=>{
      if (res.code === 200){
        // message.success('修改成功!')
        returnData(res.data)
      }else {
        message.error(res.message)
      }
    }).finally(()=>{
      getList()
    })



};
const cancel = (sid) => {
  delete editableData[sid];
  getList()
};




/* 导出事件 */
const handlerExport = () =>{
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`
  doExport( `付款通知表体${timestamp}.xlsx`,totalColumns)
}



/* 自定义设置 */
/* 显示列数据 */
const showColumns =  ref([])

/* 唯一键 */
const tableKey = ref('')

tableKey.value = window.$vueApp ? window.majesty.router.currentRoute.value.path : useRoute().path
const originalColumns = ref()




/* 自定义显示列初始化操作 */
const initCustomColumn = () => {
  // 这里是拷贝是属于
  let tempColumns = deepClone(totalColumns.value)
  let dealColumns = []
  // 使用map遍历会丢失customRender方法，所以使用forEach
  tempColumns.map((item) => {
    let newObj = Object.assign({}, item);
    newObj["visible"] = true;
    // 需要将customRender 方法追加到新对象中
    if (item.customRender) {
      newObj["customRender"] = item.customRender;
    }
    dealColumns.push(newObj);
  });

  if (props.editConfig.editStatus === editStatus.SHOW){
    dealColumns.splice(0, 1);
  }
  //原始列信息
  originalColumns.value = dealColumns;
}



/* 选中visible为true的数据进行显示 */
const customColumnChange = (settingColumns)  => {
  totalColumns.value = settingColumns.filter((item) => item.visible === true);
  if (props.editConfig.editStatus === editStatus.SHOW){
    totalColumns.value.splice(0, 1);
  }
  showColumns.value = [...totalColumns.value]

}





/* 监控 dataSourceList */
watch(dataSourceList, (newValue, oldValue) => {
  // if (props.editConfig.editStatus === editStatus.SHOW){
  //   totalColumns.value.splice(0, 1);
  // }
  showColumns.value = [...totalColumns.value];
  // 将showColumns的数据属性 和 初始属性进行比对，如果初始属性存在customRender 方法，追加到showColumns中
},{deep:true})



defineExpose({ getList });


</script>

<style lang="less" scoped>


</style>

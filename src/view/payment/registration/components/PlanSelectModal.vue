<template>
  <a-modal
      v-model:visible="props.visible"
      title="购销合同"
      width="1000px"
      @ok="handleOk"
      @cancel="handleCancel"
      :maskClosable="false"
      :keyboard="false"
      okText="保存"
      cancelText="关闭"
  >
    <!-- 查询条件 -->
    <div class="cs-search">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="购销合同号">
          <a-input
              v-model:value="searchForm.contractNo"
              placeholder="请输入购销合同号"
              allow-clear
              style="width: 200px"
          />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">查询</a-button>
          <a-button style="margin-right: 8px" @click="handleReset">重置</a-button>
        </a-form-item>
      </a-form>
    </div>

    <!-- 计划列表 -->
    <div class="table-container" :style="{ minHeight: tableHeight + 'px' }">
      <s-table
          v-if="loading || planData.length > 0"
          ref="planTableRef"
          class="cs-action-item"
          size="small"
          :scroll="{ y: tableHeight }"
          bordered
          :columns="columns"
          :data-source="planData"
          :row-key="getRowKey"
          :pagination="false"
          :loading="loading"
          :row-selection="{
          type: 'radio',
          selectedRowKeys: selectedKeys,
          onChange: onSelectChange
        }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'decTotal'">
            {{ formatNumber(record.decTotal) }}
          </template>
          <template v-if="column.dataIndex === 'qty'">
            {{ formatNumber(record.qty) }}
          </template>
          <template v-if="column.dataIndex === 'curr'">
            {{ formatCurrency(record.curr) }}
          </template>
        </template>
      </s-table>
      <div v-else class="empty-data">暂无数据</div>
    </div>
  </a-modal>
</template>

<script setup>
import {ref, reactive, watch, onMounted, h} from 'vue';
import { message } from 'ant-design-vue';
import ycCsApi from "@/api/ycCsApi";
import {insertContract} from "@/api/importedCigarettes/contract/contractApi";
import { productClassify } from '@/view/common/constant';
import { useColumnsRender } from '@/view/common/useColumnsRender';
import { useMerchant } from "@/view/common/useMerchant"
import {usePCode} from "@/view/common/usePCode";
import {insertRegistrationHead} from "@/api/payment/payment_info";
const { getPCode } = usePCode();
const { cmbShowRender} = useColumnsRender();
// 币种
const currencyOptions = ref([]);
const pCode = ref('');
// 格式化数字为千分位分隔的工具函数
const formatNumber = (value) => {
  if (value === undefined || value === null || value === '') {
    return '';
  }

  // 转换为数字并检查有效性
  const number = Number(value);
  if (isNaN(number)) {
    return '';
  }

  // 配置 NumberFormat 选项
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,  // 至少两位小数
    maximumFractionDigits: 10
  }).format(number);
};

// 格式化币种显示
const formatCurrency = (code) => {
  const option = currencyOptions.value.find(opt => opt.value === code);
  return option ? option.label : code;
};


const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'select']);

// 计划列表数据
const planData = ref([]);
const selectedKeys = ref([]);
const selectedPlan = ref(null);
const loading = ref(false);
const tableHeight = ref(300);

// 查询表单
const searchForm = reactive({
  contractNo: ''
});

// 获取供应商数据
const { merchantOptions, getMerchantOptions } = useMerchant()

// 列定义
const columns = [
  {
    title: '购销合同号',
    dataIndex: 'contractNo',
    width: 100
  },
  {
    title: '币种',
    dataIndex: 'curr',
    width: 100,
  },
  {
    title: '金额',
    dataIndex: 'decTotal',
    width: 100
  },
  {
    title: '数量',
    dataIndex: 'qty',
    width: 100
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 100
  }
];







// 生成行唯一标识
const getRowKey = (record) => {
  return `${record.contractNo}`;
};

// 获取计划列表
const getPlanList = async () => {
  loading.value = true;
  try {
    const params = {
      contractNo: searchForm.contractNo
    };
    const res = await window.majesty.httpUtil.postAction(
        `${ycCsApi.payment.registrationHead.planList}`,
        params
    );
    if (res.code === 200) {
      planData.value = res.data || [];
    } else {
      planData.value = [];
      message.error(res.message || '获取计划列表失败');
    }
  } catch (error) {
    planData.value = [];
    message.error('获取计划列表失败');
  } finally {
    console.log(planData.value)
    loading.value = false;
  }
};

// 处理查询
const handleSearch = () => {
  selectedKeys.value = [];
  selectedPlan.value = null;
  getPlanList();
};

// 处理重置
const handleReset = () => {
  searchForm.contractNo = '';
  selectedKeys.value = [];
  selectedPlan.value = null;
  getPlanList();
};

// 处理选择
const onSelectChange = (selectedRowKeys, selectedRows) => {
  selectedKeys.value = selectedRowKeys;
  selectedPlan.value = selectedRows[0];
};

// 处理确认
const handleOk = () => {
  if (!selectedPlan.value) {
    message.warning('请选择一条合同');
    return;
  }
  const params = {
    contractNo: selectedPlan.value
  };
  try {
    insertRegistrationHead(params).then((res)=>{
      if (res.code === 200){
        message.success('新增成功!')
        emit('select', res.data);
        handleCancel();
      } else {
        message.error(res.message);
      }
    })
  } catch (error) {
    console.error('新增失败', error)
  }
};

// 处理取消
const handleCancel = () => {
  selectedKeys.value = [];
  selectedPlan.value = null;
  planData.value = [];
  emit('update:visible', false);
};

// 监听visible变化，当显示时加载数据
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 设置表格高度
    tableHeight.value = 300;
    getPlanList();
  } else {
    planData.value = [];
    selectedKeys.value = [];
    selectedPlan.value = null;
  }
});

// 组件卸载时清理数据
onMounted(() => {
  planData.value = [];
  selectedKeys.value = [];
  selectedPlan.value = null;

  console.log(currencyOptions)

  // 获取币种代码
  getPCode().then(res => {
    console.log('res', res)
    pCode.value = res;
    currencyOptions.value = Object.entries(pCode.value.CURR).map(([value, label]) => ({
      label: `${value} ${label}`,
      value
    }));
  })


  console.log(currencyOptions)
});
</script>

<style lang="less" scoped>
.cs-search {
  margin-bottom: 16px;
}

.table-container {
  position: relative;
  min-height: 300px;
}

.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 200px;
  color: #999;
  font-size: 14px;
}

:deep(.ant-table-wrapper) {
  .ant-table {
    .ant-table-row {
      &.ant-table-row-selected > td {
        background-color: #e6f7ff;
      }
      &:hover > td {
        background-color: #fafafa;
      }
    }
  }
}

:deep(.ant-radio-wrapper) {
  .ant-radio-checked {
    .ant-radio-inner {
      border-color: #1890ff;
      &::after {
        background-color: #1890ff;
      }
    }
  }
}
</style>

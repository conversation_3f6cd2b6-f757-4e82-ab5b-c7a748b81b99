<template>
  <section  >
    <a-card size="small" title="报价单信息" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '160px' } }" :rules="rules"
                :model="formData"   class=" grid-container">

          <a-form-item name="businessType" :label="'业务类型'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear @change="handleChangeGNameList"  show-search v-model:value="formData.businessType" id="businessType">
              <a-select-option v-for="item in productClassify.businessType2"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="gName" :label="'商品名称'" class="grid-item" :colon="false">
            <a-select
              v-model:value="formData.gName"
              :disabled="showDisable"
              style="width: 100%"
              size="small"
              placeholder="Please select"
              :options="gNameList"
              @change="handleChangeGName"
            ></a-select>
          </a-form-item>
<!--          <a-form-item name="gName" :label="'商品名称'" class="grid-item" :colon="false">-->
<!--            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.gName" id="gName">-->
<!--              <a-select-option v-for="item in gNameList"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">-->
<!--                {{item.value}} {{item.label }}-->
<!--              </a-select-option>-->
<!--            </cs-select>-->
<!--          </a-form-item>-->
          <a-form-item name="merchandiseCategories" :label="'商品类别'" class="grid-item" :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.merchandiseCategories" id="merchandiseCategories">
              <a-select-option v-for="item in merchandiseCategoriesMap"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="productModel" :label="'产品型号'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.productModel"/>
          </a-form-item>
          <a-form-item name="specifications" :label="'规格'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.specifications"/>
          </a-form-item>
          <a-form-item name="grammage" :label="'克重'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              // 分别处理整数部分和小数部分
                              const parts = value.toString().split('.');
                              // 只对整数部分添加千位分隔符
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              // 组合整数和小数部分
                              return parts.join('.');
                            }"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.grammage" notConvertNumber decimal int-length="14" precision="5"/>
          </a-form-item>
          <a-form-item name="materialNo" :label="'材料编号'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.materialNo"/>
          </a-form-item>
          <a-form-item name="unit" :label="'计量单位'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.unit" id="unit">
              <a-select-option class="cs-select-dropdown" v-for="item in unitList"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="unitI" :label="'进口计量单位'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.unitI" id="unitI">
              <a-select-option class="cs-select-dropdown" v-for="item in unitList"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
<!--          <a-form-item name="unit" :label="'计量单位'" class="grid-item" :colon="false">-->
<!--            <a-input :disabled="showDisable" size="small" v-model:value="formData.unit"/>-->
<!--          </a-form-item>-->
<!--          <a-form-item name="unitI" :label="'进口计量单位'" class="grid-item" :colon="false">-->
<!--            <a-input :disabled="showDisable" size="small" v-model:value="formData.unitI"/>-->
<!--          </a-form-item>-->
          <a-form-item name="merchantCode" :label="'供应商'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.merchantCode" id="merchantCode">
              <a-select-option class="cs-select-dropdown" v-for="item in supplierList"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="priceTerm" :label="'价格条款'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.priceTerm" id="priceTerm">
              <a-select-option class="cs-select-dropdown" v-for="item in priceTermList"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="destinationPort" :label="'指运港'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.destinationPort" id="destinationPort">
              <a-select-option class="cs-select-dropdown" v-for="item in portList"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="importUnitPrice" :label="'进口单价'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            @input="allChange"
                            v-model:value="formData.importUnitPrice" notConvertNumber decimal int-length="11" precision="8"/>
          </a-form-item>
          <a-form-item name="unitTrayPrice" :label="'单价/盘（USD）'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.unitTrayPrice" notConvertNumber decimal int-length="11" precision="8"/>
          </a-form-item>
          <a-form-item name="curr" :label="'币种'" class="grid-item" :colon="false">
            <a-select
              v-model:value="formData.curr"
              :disabled="showDisable"
              style="width: 100%"
              size="small"
              placeholder="Please select"
              :options="currMap"
            ></a-select>
          </a-form-item>
          <a-form-item name="exchangeRate" :label="'汇率'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            @input="allChange"
                            v-model:value="formData.exchangeRate" notConvertNumber decimal int-length="11" precision="6"/>
          </a-form-item>
          <a-form-item name="tariffRate" :label="'关税率%'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable" size="small" v-model:value="formData.tariffRate"
                            :formatter="value => `${value}%`"
                            :parser="value => value.replace('%', '')"
                            @input="tariffPriceChange"
                            notConvertNumber decimal int-length="13" precision="6"/>
          </a-form-item>
          <a-form-item name="tariffPrice" :label="'关税金额'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.tariffPrice" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="vat" :label="'增值税率% '" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable" size="small" v-model:value="formData.vat"
                            :formatter="value => `${value}%`"
                            :parser="value => value.replace('%', '')"
                            @input="allChange"
                            notConvertNumber decimal int-length="13" precision="6"/>
          </a-form-item>
          <a-form-item name="headAgencyFeeRate" :label="'总公司代理费率%'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable" size="small" v-model:value="formData.headAgencyFeeRate"
                            :formatter="value => `${value}%`"
                            :parser="value => value.replace('%', '')"
                            @input="headAgencyFeePriceChange"
                            notConvertNumber decimal int-length="15" precision="4"/>
          </a-form-item>
          <a-form-item name="headAgencyFeePrice" :label="'总公司代理费 '" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.headAgencyFeePrice" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="freightForwardingFeePrice" :label="'货代费单价'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            @input="freightForwardingFeePriceChange"
                            v-model:value="formData.freightForwardingFeePrice" notConvertNumber decimal int-length="15" precision="4"/>
          </a-form-item>
          <a-form-item name="freightForwardingFee" :label="'货代费用'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.freightForwardingFee" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="insuranceFee" :label="'保险费'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.insuranceFee" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="purchaseCost" :label="'购进成本'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.purchaseCost" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="storageTransportTax" :label="'仓储运输及税额'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.storageTransportTax" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="grossMargin" :label="'毛利'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.grossMargin" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="priceExcludingTax" :label="'不含税单价'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.priceExcludingTax" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="priceRmb" :label="'人民币单价（含税）'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.priceRmb" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>

          <a-form-item name="note" :label="'备注'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.note"/>
          </a-form-item>
          <a-form-item name="createrUserName" :label="'制单人'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.createrUserName"/>
          </a-form-item>
          <!--          制单时间-->
          <a-form-item name="createrTime" :label="'制单时间'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="true"
              v-model:value="formData.createrTime"
              id="insertTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>
          <a-form-item name="status" :label="'数据状态'" class="grid-item"  :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.status" id="status">
              <a-select-option v-for="item in productClassify.dataStatus"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !== 'SHOW' ">保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(false)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>


  </section>
</template>

<script setup>
import {editStatus,productClassify} from '@/view/common/constant'
import {message} from "ant-design-vue";
import {onMounted, reactive, ref, watch} from "vue";
import {usePCode} from "@/view/common/usePCode";
import {getGName, getOrderSupplierList, insertQuoList, updateQuoList} from "@/api/cs_api_constant";
import {isNullOrEmpty} from "@/view/utils/common";
import 'vue-multiselect/dist/vue-multiselect.min.css';
import CsSelect from "@/components/select/CsSelect.vue";
import {getMerchantCodeValueClient} from "@/api/bi/bi_client_info";
import ycCsApi from "@/api/ycCsApi";
const { getPCode } = usePCode()

const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onBack']);

const onBack = (val) => {
  emit('onBack', val);
};

// 是否禁用
const showDisable = ref(false)

// 表单数据
const formData = reactive({
  sid:'',
  businessType: '2',
  gName: '',
  merchandiseCategories: '',
  productModel: '',
  specifications: '',
  grammage: '',
  materialNo: '',
  unit: '',
  unitI: '',
  merchantCode: '',
  priceTerm: 'CFR',
  destinationPort: 'CHN331',
  importUnitPrice: '',
  unitTrayPrice: '',
  curr: 'USD',
  exchangeRate: '7.2',
  tariffRate: '7.5',
  tariffPrice: '',
  vat: '13',
  headAgencyFeeRate: '1.5',
  headAgencyFeePrice: '',
  freightForwardingFeePrice: '450',
  freightForwardingFee: '',
  insuranceFee: '',
  purchaseCost: '',
  storageTransportTax: '',
  grossMargin: '',
  priceExcludingTax: '',
  priceRmb: '',
  note: '',
  status: '0',
  createrBy: '',
  createrUserName: '',
  createrTime: '',

})
// 校验规则
const rules = {

  businessType: [
    { required: true, message: '业务类型不能为空！', trigger: 'blur' },
  ],
  gName: [
    { required: true, message: '商品名称不能为空！', trigger: 'blur' },
  ],
  merchandiseCategories: [
    { required: true, message: '商品类别不能为空！', trigger: 'blur' },
  ],
  productModel: [
    { max: 80, message: '商品型号长度不能超过80位字节', trigger: 'blur'},
  ],
  specifications: [
    { max: 200, message: '规格长度不能超过200位字节', trigger: 'blur'},
    { required: true, message: '规格不能为空！', trigger: 'blur' },
  ],
  materialNo: [
    { max: 50, message: '材料编号长度不能超过80位字节', trigger: 'blur'},
  ],
  unit: [
    { max: 20, message: '计量单位长度不能超过20位字节', trigger: 'blur'},
    { required: true, message: '计量单位不能为空！', trigger: 'blur' },
  ],
  unitI: [
    { max: 20, message: '进口计量单位长度不能超过20位字节', trigger: 'blur'},
    { required: true, message: '进口计量单位不能为空！', trigger: 'blur' },
  ],
  merchantCode: [
    { required: true, message: '客商编号不能为空！', trigger: 'blur' },
  ],
  priceTerm: [],
  destinationPort: [],
  importUnitPrice: [
    { required: true, message: '进口单价不能为空！', trigger: 'blur' },
  ],
  unitTrayPrice: [],
  curr: [
    { required: true, message: '币种不能为空！', trigger: 'blur' },
  ],
  exchangeRate: [
    { required: true, message: '汇率不能为空！', trigger: 'blur' },
  ],
  tariffRate: [
    { required: true, message: '关税率不能为空！', trigger: 'blur' },
  ],
  tariffPrice: [
    { required: true, message: '关税金额不能为空！', trigger: 'blur' },
  ],
  vat: [
    { required: true, message: '增值税率不能为空！', trigger: 'blur' },
  ],
  headAgencyFeeRate: [
    { required: true, message: '总公司代理费率不能为空！', trigger: 'blur' },
  ],
  headAgencyFeePrice: [
    { required: true, message: '总公司代理费金额不能为空！', trigger: 'blur' },
  ],
  freightForwardingFeePrice: [
    { required: true, message: '货代费单价不能为空！', trigger: 'blur' },
  ],
  freightForwardingFee: [
    { required: true, message: '货代费用不能为空！', trigger: 'blur' },
  ],
  insuranceFee: [],
  purchaseCost: [
    { required: true, message: '购进成本不能为空！', trigger: 'blur' },
  ],
  storageTransportTax: [
    { required: true, message: '仓储运输及税额不能为空！', trigger: 'blur' },
  ],
  grossMargin: [
    { required: true, message: '毛利不能为空！', trigger: 'blur' },
  ],
  priceExcludingTax: [
    { required: true, message: '不含税单价不能为空！', trigger: 'blur' },
  ],
  priceRmb: [
    { required: true, message: '人民币单价（含税）不能为空！', trigger: 'blur' },
  ],
  status: [
    { required: true, message: '数据状态不能为空！', trigger: 'blur' },
  ],
  note: [
    {max: 200, message: '备注长度不能超过200位字节', trigger: 'blur'}
  ]

}

const pCode = ref('')
// 初始化操作
onMounted(() => {
  getSupplierList();
  getUnitOptions();
  getGNameList();
  getPortOptions();
  getPriceTermOptions();
  getPCode().then(res=>{
    console.log('res',res)
    pCode.value = res;
    currMap.value = Object.entries(pCode.value.CURR).map(([value, label]) => ({
      value
    }));
  })
  getMerchantCodeValueClient().then((res)=>{
    if (res.code === 200){
      //商品类别
      if (typeof(res.data.merchandiseCategories) !== "undefined"){
        res.data.merchandiseCategories.map(item => {
          merchandiseCategoriesMap.value.push({
            label: item.value,
            value: item.label
          })
        })
      }
    }
  })
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    showDisable.value = false
    Object.assign(formData, {});
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
});
const getGNameList = () => {
  getGName(formData.businessType).then(res=>{
    gNameList.value = []
    gNameListToMerchandiseCategories.value = []
    if (!isNullOrEmpty(res.data)){
      res.data.map(item => {
        gNameList.value.push({
          // label: item.gName,
          value: item.gName
        })
        gNameListToMerchandiseCategories.value.push({
          label: item.gName,
          value: item.merchandiseCategories
        })
      })
    }
  })
}
const getSupplierList  = () =>{
  getOrderSupplierList({}).then(res=>{
    // console.log('获取供应商信息未',res)
    if (!isNullOrEmpty(res.data)){
      supplierList.value = res.data
    }
  })
}
const getPortOptions = async () => {
  try {
    const params = {paramsType: "PORT"}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.baseInfoCustomerParams.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        portList.value.push({
          value: item.paramsCode,
          label: item.paramsName
        });
      });
    }
  } catch (error) {
  }
}
const getUnitOptions = async () => {
  try {
    const params = {paramsType: "UNIT"}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.baseInfoCustomerParams.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        unitList.value.push({
          value: item.paramsCode,
          label: item.paramsName
        });
      });
    }
  } catch (error) {
  }
}
const getPriceTermOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.params.priceTerms.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        priceTermList.value.push({
          value: item.paramCode,
          label: item.priceTerm
        });
      });
    }
  } catch (error) {
  }
}
const merchandiseCategoriesMap = ref([])
const gNameListToMerchandiseCategories = ref([])
const supplierList = ref([])
const gNameList = ref([])
const currMap = ref([])
const portList = ref([])
const priceTermList = ref([])
const unitList = ref([])
// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
const formRef = ref(null);
// 保存
const handlerSave = () => {
  formRef.value
    .validate()
    .then(() => {
      if (props.editConfig && props.editConfig.editStatus === editStatus.ADD){
        insertQuoList(formData).then((res)=>{
          if (res.code === 200){
            message.success('新增成功!')
            onBack(true)
          }else {
            message.error(res.message)
          }
        })
      }else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT){
        console.log('value',formData)
        updateQuoList(formData.sid,formData).then((res)=>{
          if (res.code === 200){
            message.success('修改成功!')
            onBack(true)
          }else {
            message.error(res.message)
          }
        })
      }
    })
    .catch(error => {
      console.log('validate failed', error);
    })
};

const handleChangeGName = () => {
  formData.gname = formData.gName
  if(formData.gName !== null && formData.gName !== '' && formData.gName !== undefined && gNameListToMerchandiseCategories.value.length > 0){
    formData.merchandiseCategories = gNameListToMerchandiseCategories.value.find(item => item.label === formData.gName).value
  }
};
const handleChangeGNameList = () =>{
  if(formData.businessType !== null && formData.businessType !== '' && formData.businessType !== undefined){
    getGNameList();
  }else {
    gNameList.value = []
    gNameListToMerchandiseCategories.value = []
  }
};
const allChange  = () =>{
  freightForwardingFeePriceChange();
  tariffPriceChange();
  headAgencyFeePriceChange();
  insuranceFeeChange();
  purchaseCostChange();
  storageTransportTaxChange();
  priceRmbChange();
  grossMarginChange();
  priceExcludingTaxChange();
}

const tariffPriceChange = () =>{
  if(formData.tariffPrice !== null){
    delete formData.tariffPrice;
  }
  if(formData.importUnitPrice !== null && formData.exchangeRate !== null && formData.tariffRate !== null){
    formData.tariffPrice = tariffPriceCount(formData)
  }
}
const headAgencyFeePriceChange = () =>{
  if(formData.headAgencyFeePrice !== null){
    delete formData.headAgencyFeePrice;
  }
  if(formData.importUnitPrice !== null && formData.exchangeRate !== null && formData.headAgencyFeeRate !== null){
    formData.headAgencyFeePrice = headAgencyFeePriceCount(formData)
  }
}
const freightForwardingFeePriceChange = () =>{
  if(formData.freightForwardingFee !== null){
    delete formData.freightForwardingFee;
  }
  if(formData.freightForwardingFeePrice !== null){
    const fee = parseFloat(formData.freightForwardingFeePrice);
    formData.freightForwardingFee = roundToDecimal(fee/1000,2)
  }
}
const insuranceFeeChange = () =>{
  if(formData.insuranceFee !== null){
    delete formData.insuranceFee;
  }
  if(formData.importUnitPrice !== null && formData.exchangeRate !== null){
    formData.insuranceFee = insuranceFeeCount(formData)
  }
}
const purchaseCostChange = () =>{
  if(formData.purchaseCost !== null){
    delete formData.purchaseCost;
  }
  if(formData.importUnitPrice !== null && formData.exchangeRate !== null && formData.tariffPrice !== null && formData.vat !== null){
    formData.purchaseCost = purchaseCostCount(formData)
  }
}
const storageTransportTaxChange = () =>{
  if(formData.storageTransportTax !== null){
    delete formData.storageTransportTax;
  }
  if(formData.freightForwardingFee !== null && formData.insuranceFee !== null && formData.tariffPrice !== null && formData.vat !== null){
    formData.storageTransportTax = storageTransportTaxCount(formData)
  }
}
const priceRmbChange = () =>{
  if(formData.priceRmb !== null){
    delete formData.priceRmb;
  }
  if(formData.purchaseCost !== null && formData.storageTransportTax !== null){
    formData.priceRmb = priceRmbCount(formData)
  }
}
const grossMarginChange = () =>{
  if(formData.grossMargin !== null){
    delete formData.grossMargin;
  }
  if(formData.priceRmb !== null && formData.purchaseCost !== null && formData.storageTransportTax !== null){
    formData.grossMargin = grossMarginCount(formData)
  }
}

const priceExcludingTaxChange = () =>{
  if(formData.priceExcludingTax !== null){
    delete formData.priceExcludingTax;
  }
  if(formData.priceRmb !== null && formData.vat !== null){
    formData.priceExcludingTax = priceExcludingTaxCount(formData)
  }
}

const tariffPriceCount = (row) => {
  const importUnitPrice = parseFloat(row.importUnitPrice);
  const exchangeRate = parseFloat(row.exchangeRate);
  const tariffRate = parseFloat(row.tariffRate);
  const tariffPrice = roundToDecimal((tariffRate/100)*exchangeRate*importUnitPrice,2)
  return tariffPrice !== null ? tariffPrice : null
};
const headAgencyFeePriceCount = (row) => {
  const importUnitPrice = parseFloat(row.importUnitPrice);
  const exchangeRate = parseFloat(row.exchangeRate);
  const headAgencyFeeRate = parseFloat(row.headAgencyFeeRate);
  const headAgencyFeePrice = roundToDecimal((headAgencyFeeRate/100)*exchangeRate*importUnitPrice,2)
  return headAgencyFeePrice !== null ? headAgencyFeePrice : null
};
const insuranceFeeCount = (row) => {
  const importUnitPrice = parseFloat(row.importUnitPrice);
  const exchangeRate = parseFloat(row.exchangeRate);
  const insuranceFee = roundToDecimal((0.0219/100)*1.1*exchangeRate*importUnitPrice,2)
  return insuranceFee !== null ? insuranceFee : null
};
const purchaseCostCount = (row) => {
  const importUnitPrice = parseFloat(row.importUnitPrice);
  const exchangeRate = parseFloat(row.exchangeRate);
  const tariffPrice = parseFloat(row.tariffPrice);
  const vat = parseFloat(row.vat);
  const purchaseCost = roundToDecimal(((exchangeRate*importUnitPrice)+tariffPrice)*vat/100,2)
  return purchaseCost !== null ? purchaseCost : null
};
const storageTransportTaxCount = (row) => {
  const freightForwardingFee = parseFloat(row.freightForwardingFee);
  const insuranceFee = parseFloat(row.insuranceFee);
  const tariffPrice = parseFloat(row.tariffPrice);
  const vat = parseFloat(row.vat);
  const storageTransportTax = roundToDecimal((freightForwardingFee+insuranceFee+tariffPrice)*vat/100,2)
  return storageTransportTax !== null ? storageTransportTax : null
};
const priceRmbCount = (row) => {
  const purchaseCost = parseFloat(row.purchaseCost);
  const storageTransportTax = parseFloat(row.storageTransportTax);
  const priceRmb = roundToDecimal((purchaseCost+storageTransportTax)/0.985,2)
  return priceRmb !== null ? priceRmb : null
};

const grossMarginCount = (row) => {
  const priceRmb = parseFloat(row.priceRmb);
  const purchaseCost = parseFloat(row.purchaseCost);
  const storageTransportTax = parseFloat(row.storageTransportTax);
  const grossMargin = roundToDecimal(priceRmb-purchaseCost-storageTransportTax,2)
  return grossMargin !== null ? grossMargin : null
};
const priceExcludingTaxCount = (row) => {
  const vat = parseFloat(row.vat);
  const priceRmb = parseFloat(row.priceRmb);
  const priceExcludingTax = roundToDecimal(priceRmb/(100+vat)*100,2)
  return priceExcludingTax !== null ? priceExcludingTax : null
};
function roundToDecimal(num, decimals) {
  const factor = Math.pow(10, decimals);
  return Math.round(num * factor) / factor;
}

</script>

<style lang="less" scoped>


</style>



